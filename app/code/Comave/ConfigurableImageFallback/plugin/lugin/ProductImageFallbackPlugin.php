<?php
// app/code/Vendor/ConfigurableImageFallback/Plugin/ProductImageFallbackPlugin.php

namespace Vendor\ConfigurableImageFallback\Plugin;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product\Type as ProductType;
use Magento\CatalogGraphQl\Model\Resolver\Product\Image as Subject;
use Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable as ConfigurableResource;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\Resolver\ContextInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class ProductImageFallbackPlugin
{
    private ProductRepositoryInterface $productRepository;
    private ConfigurableResource     $configurableResource;

    public function __construct(
        ProductRepositoryInterface $productRepository,
        ConfigurableResource       $configurableResource
    ) {
        $this->productRepository     = $productRepository;
        $this->configurableResource = $configurableResource;
    }

    /**
     * If this is a “simple” child of a configurable that has no
     * media entries of its own, replace the model with its parent.
     */
    public function aroundResolve(
        Subject  $subject,
        callable $proceed,
        Field    $field,
        $context,
        ResolveInfo $info,
        array    $value = null,
        array    $args  = null
    ) {
        /** @var \Magento\Catalog\Model\Product $child */
        $child = $value['model'] ?? null;

        if ($child && $child->getTypeId() === ProductType::TYPE_SIMPLE) {
            // only true media entries; placeholder images aren't in galleryEntries
            $entries = $child->getMediaGalleryEntries();
            if (empty($entries)) {
                $parentIds = $this->configurableResource->getParentIdsByChild($child->getId());
                if (!empty($parentIds)) {
                    $parent = $this->productRepository->getById($parentIds[0]);
                    // swap in the parent so Magento’s core resolver will use its image
                    $value['model'] = $parent;
                }
            }
        }

        return $proceed($field, $context, $info, $value, $args);
    }
}
