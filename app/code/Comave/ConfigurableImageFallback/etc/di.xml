<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    
    <!-- Plugin for ProductImage resolver (handles 'image' field) -->
    <type name="Magento\CatalogGraphQl\Model\Resolver\Product\ProductImage">
        <plugin name="configurable_image_fallback_product_image" 
                type="Comave\ConfigurableImageFallback\Plugin\ProductImageFallbackPlugin" 
                sortOrder="10"/>
    </type>
    
    <!-- Plugin for MediaGalleryEntries resolver (handles 'media_gallery' field) -->
    <type name="Magento\CatalogGraphQl\Model\Resolver\Product\MediaGalleryEntries">
        <plugin name="configurable_image_fallback_media_gallery" 
                type="Comave\ConfigurableImageFallback\Plugin\ProductImageFallbackPlugin" 
                sortOrder="10"/>
    </type>
    
</config>
