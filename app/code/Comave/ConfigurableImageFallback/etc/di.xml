<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <!-- Plugin for Product model to handle media gallery fallback -->
    <type name="Magento\Catalog\Model\Product">
        <plugin name="configurable_image_fallback_product_media"
                type="Comave\ConfigurableImageFallback\Plugin\ProductMediaGalleryFallbackPlugin"
                sortOrder="10"/>
    </type>

    <!-- Plugin for GraphQL ProductImage resolver to test if it's being called -->
    <type name="Magento\CatalogGraphQl\Model\Resolver\Product\ProductImage">
        <plugin name="configurable_image_fallback_graphql_test"
                type="Comave\ConfigurableImageFallback\Plugin\ProductMediaGalleryFallbackPlugin"
                sortOrder="10"/>
    </type>

    <!-- Plugin for GraphQL MediaGalleryEntries resolver to test if it's being called -->
    <type name="Magento\CatalogGraphQl\Model\Resolver\Product\MediaGalleryEntries">
        <plugin name="configurable_image_fallback_media_gallery_test"
                type="Comave\ConfigurableImageFallback\Plugin\ProductMediaGalleryFallbackPlugin"
                sortOrder="10"/>
    </type>

</config>
