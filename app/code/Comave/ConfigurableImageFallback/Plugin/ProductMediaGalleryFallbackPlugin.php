<?php

namespace Comave\ConfigurableImageFallback\Plugin;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product\Type as ProductType;
use Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable as ConfigurableResource;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class ProductMediaGalleryFallbackPlugin
{
    private ProductRepositoryInterface $productRepository;
    private ConfigurableResource $configurableResource;

    public function __construct(
        ProductRepositoryInterface $productRepository,
        ConfigurableResource $configurableResource
    ) {
        $this->productRepository = $productRepository;
        $this->configurableResource = $configurableResource;
    }

    /**
     * Test plugin to see if it's being called at all
     */
    public function beforeGetMediaGalleryEntries(
        \Magento\Catalog\Model\Product $subject
    ) {
        error_log('ConfigurableImageFallback: BEFORE getMediaGalleryEntries called for product: ' . $subject->getSku() . ' (ID: ' . $subject->getId() . ', Type: ' . $subject->getTypeId() . ')');
    }

    /**
     * If this is a "simple" child of a configurable that has no
     * media entries of its own, return the parent's media gallery entries.
     */
    public function afterGetMediaGalleryEntries(
        \Magento\Catalog\Model\Product $subject,
        $result
    ) {
        // Simple file logging that will definitely work
        error_log('ConfigurableImageFallback: Plugin called for product: ' . $subject->getSku() . ' (ID: ' . $subject->getId() . ', Type: ' . $subject->getTypeId() . ', Has media: ' . (!empty($result) ? 'YES' : 'NO') . ')');

        // If the product already has media gallery entries, return them as-is
        if (!empty($result)) {
            error_log('ConfigurableImageFallback: Product ' . $subject->getSku() . ' has ' . count($result) . ' media entries, returning original');
            return $result;
        }

        // Only apply fallback for simple products (variants)
        if ($subject->getTypeId() !== ProductType::TYPE_SIMPLE) {
            error_log('ConfigurableImageFallback: Product ' . $subject->getSku() . ' is not simple (' . $subject->getTypeId() . '), skipping fallback');
            return $result;
        }

        error_log('ConfigurableImageFallback: Simple product ' . $subject->getSku() . ' with no media entries, checking for parent');

        try {
            // Get parent configurable product IDs
            $parentIds = $this->configurableResource->getParentIdsByChild($subject->getId());
            error_log('ConfigurableImageFallback: Found parent IDs for ' . $subject->getSku() . ': ' . implode(',', $parentIds));

            if (!empty($parentIds)) {
                $parent = $this->productRepository->getById($parentIds[0]);
                $parentEntries = $parent->getMediaGalleryEntries();

                error_log('ConfigurableImageFallback: Parent product loaded - Child: ' . $subject->getSku() . ', Parent: ' . $parent->getSku() . ' (ID: ' . $parentIds[0] . '), Parent entries: ' . count($parentEntries));

                if (!empty($parentEntries)) {
                    error_log('ConfigurableImageFallback: SUCCESS! Returning ' . count($parentEntries) . ' parent media entries for child ' . $subject->getSku() . ' from parent ' . $parent->getSku());
                    return $parentEntries;
                } else {
                    error_log('ConfigurableImageFallback: Parent ' . $parent->getSku() . ' has no media entries either');
                }
            } else {
                error_log('ConfigurableImageFallback: No parent found for simple product ' . $subject->getSku());
            }
        } catch (\Exception $e) {
            error_log('ConfigurableImageFallback: ERROR loading parent product for ' . $subject->getSku() . ': ' . $e->getMessage());
        }

        error_log('ConfigurableImageFallback: Returning original empty result for ' . $subject->getSku());

        return $result;
    }

    /**
     * Test plugin for getData method
     */
    public function afterGetData(
        \Magento\Catalog\Model\Product $subject,
        $result,
        $key = '',
        $index = null
    ) {
        if ($key === 'image' || $key === 'small_image' || $key === 'thumbnail') {
            error_log('ConfigurableImageFallback: getData called for ' . $key . ' on product: ' . $subject->getSku() . ' (Type: ' . $subject->getTypeId() . '), Result: ' . $result);
        }
        return $result;
    }

    /**
     * Test plugin for getImage method
     */
    public function afterGetImage(
        \Magento\Catalog\Model\Product $subject,
        $result
    ) {
        error_log('ConfigurableImageFallback: getImage called for product: ' . $subject->getSku() . ' (Type: ' . $subject->getTypeId() . '), Result: ' . $result);
        return $result;
    }

    /**
     * Test plugin for GraphQL ProductImage resolver
     */
    public function beforeResolve(
        $subject,
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (isset($value['model'])) {
            $product = $value['model'];
            error_log('ConfigurableImageFallback: GraphQL resolver called for product: ' . $product->getSku() . ' (Type: ' . $product->getTypeId() . '), Field: ' . $field->getName());
        } else {
            error_log('ConfigurableImageFallback: GraphQL resolver called but no product model found, Field: ' . $field->getName());
        }
    }
}
