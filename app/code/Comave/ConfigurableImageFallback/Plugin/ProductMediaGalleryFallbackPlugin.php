<?php

namespace Comave\ConfigurableImageFallback\Plugin;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product\Type as ProductType;
use Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable as ConfigurableResource;

class ProductMediaGalleryFallbackPlugin
{
    private ProductRepositoryInterface $productRepository;
    private ConfigurableResource $configurableResource;

    public function __construct(
        ProductRepositoryInterface $productRepository,
        ConfigurableResource $configurableResource
    ) {
        $this->productRepository = $productRepository;
        $this->configurableResource = $configurableResource;
    }

    /**
     * If this is a "simple" child of a configurable that has no
     * media entries of its own, return the parent's media gallery entries.
     */
    public function afterGetMediaGalleryEntries(
        \Magento\Catalog\Model\Product $subject,
        $result
    ) {
        // If the product already has media gallery entries, return them as-is
        if (!empty($result)) {
            return $result;
        }

        // Only apply fallback for simple products (variants)
        if ($subject->getTypeId() !== ProductType::TYPE_SIMPLE) {
            return $result;
        }

        try {
            // Get parent configurable product IDs
            $parentIds = $this->configurableResource->getParentIdsByChild($subject->getId());
            if (!empty($parentIds)) {
                $parent = $this->productRepository->getById($parentIds[0]);
                $parentEntries = $parent->getMediaGalleryEntries();
                
                if (!empty($parentEntries)) {
                    return $parentEntries;
                }
            }
        } catch (\Exception $e) {
            // If we can't load the parent, return the original empty result
        }

        return $result;
    }
}
