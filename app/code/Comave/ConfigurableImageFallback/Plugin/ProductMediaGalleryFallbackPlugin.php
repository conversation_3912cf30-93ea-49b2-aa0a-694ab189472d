<?php

namespace Comave\ConfigurableImageFallback\Plugin;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product\Type as ProductType;
use Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable as ConfigurableResource;
use Psr\Log\LoggerInterface;

class ProductMediaGalleryFallbackPlugin
{
    private ProductRepositoryInterface $productRepository;
    private ConfigurableResource $configurableResource;
    private LoggerInterface $logger;

    public function __construct(
        ProductRepositoryInterface $productRepository,
        ConfigurableResource $configurableResource,
        LoggerInterface $logger
    ) {
        $this->productRepository = $productRepository;
        $this->configurableResource = $configurableResource;
        $this->logger = $logger;
    }

    /**
     * If this is a "simple" child of a configurable that has no
     * media entries of its own, return the parent's media gallery entries.
     */
    public function afterGetMediaGalleryEntries(
        \Magento\Catalog\Model\Product $subject,
        $result
    ) {
        $this->logger->info('ConfigurableImageFallback: Plugin called', [
            'product_id' => $subject->getId(),
            'product_sku' => $subject->getSku(),
            'product_type' => $subject->getTypeId(),
            'has_media_entries' => !empty($result),
            'media_entries_count' => count($result)
        ]);

        // If the product already has media gallery entries, return them as-is
        if (!empty($result)) {
            $this->logger->info('ConfigurableImageFallback: Product has media entries, returning original', [
                'product_sku' => $subject->getSku(),
                'entries_count' => count($result)
            ]);
            return $result;
        }

        // Only apply fallback for simple products (variants)
        if ($subject->getTypeId() !== ProductType::TYPE_SIMPLE) {
            $this->logger->info('ConfigurableImageFallback: Not a simple product, skipping fallback', [
                'product_sku' => $subject->getSku(),
                'product_type' => $subject->getTypeId()
            ]);
            return $result;
        }

        $this->logger->info('ConfigurableImageFallback: Simple product with no media entries, checking for parent', [
            'product_sku' => $subject->getSku()
        ]);

        try {
            // Get parent configurable product IDs
            $parentIds = $this->configurableResource->getParentIdsByChild($subject->getId());
            $this->logger->info('ConfigurableImageFallback: Found parent IDs', [
                'product_sku' => $subject->getSku(),
                'parent_ids' => $parentIds
            ]);

            if (!empty($parentIds)) {
                $parent = $this->productRepository->getById($parentIds[0]);
                $parentEntries = $parent->getMediaGalleryEntries();

                $this->logger->info('ConfigurableImageFallback: Parent product loaded', [
                    'child_sku' => $subject->getSku(),
                    'parent_id' => $parentIds[0],
                    'parent_sku' => $parent->getSku(),
                    'parent_entries_count' => count($parentEntries)
                ]);

                if (!empty($parentEntries)) {
                    $this->logger->info('ConfigurableImageFallback: Returning parent media entries', [
                        'child_sku' => $subject->getSku(),
                        'parent_sku' => $parent->getSku(),
                        'entries_count' => count($parentEntries)
                    ]);
                    return $parentEntries;
                } else {
                    $this->logger->info('ConfigurableImageFallback: Parent has no media entries either', [
                        'child_sku' => $subject->getSku(),
                        'parent_sku' => $parent->getSku()
                    ]);
                }
            } else {
                $this->logger->info('ConfigurableImageFallback: No parent found for simple product', [
                    'product_sku' => $subject->getSku()
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->error('ConfigurableImageFallback: Error loading parent product', [
                'product_sku' => $subject->getSku(),
                'error' => $e->getMessage()
            ]);
        }

        $this->logger->info('ConfigurableImageFallback: Returning original empty result', [
            'product_sku' => $subject->getSku()
        ]);

        return $result;
    }
}
