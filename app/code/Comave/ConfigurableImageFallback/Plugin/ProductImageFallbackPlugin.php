<?php

namespace Comave\ConfigurableImageFallback\Plugin;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product\Type as ProductType;
use Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable as ConfigurableResource;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class ProductImageFallbackPlugin
{
    private ProductRepositoryInterface $productRepository;
    private ConfigurableResource $configurableResource;

    public function __construct(
        ProductRepositoryInterface $productRepository,
        ConfigurableResource $configurableResource
    ) {
        $this->productRepository = $productRepository;
        $this->configurableResource = $configurableResource;
    }

    /**
     * If this is a "simple" child of a configurable that has no
     * media entries of its own, replace the model with its parent.
     */
    public function aroundResolve(
        $subject,
        callable $proceed,
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        /** @var \Magento\Catalog\Model\Product $child */
        $child = $value['model'] ?? null;

        if ($child && $child->getTypeId() === ProductType::TYPE_SIMPLE) {
            // Check if this simple product has any media gallery entries
            $entries = $child->getMediaGalleryEntries();
            if (empty($entries)) {
                // Get parent configurable product IDs
                $parentIds = $this->configurableResource->getParentIdsByChild($child->getId());
                if (!empty($parentIds)) {
                    try {
                        $parent = $this->productRepository->getById($parentIds[0]);
                        // Check if parent has images before swapping
                        if (!empty($parent->getMediaGalleryEntries())) {
                            // Swap in the parent so Magento's core resolver will use its image
                            $value['model'] = $parent;
                        }
                    } catch (\Exception $e) {
                        // If we can't load the parent, continue with the original child
                    }
                }
            }
        }

        return $proceed($field, $context, $info, $value, $args);
    }
}
