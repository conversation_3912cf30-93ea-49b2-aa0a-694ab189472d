<?php
namespace Comave\CatalogGraphQl\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\App\State;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class CheckProduct extends Command
{
    private const SKU = 'sku';
    private const ID = 'id';
    
    /**
     * @var ResourceConnection
     */
    private $resourceConnection;
    
    /**
     * @var State
     */
    private $state;
    
    /**
     * @var StoreManagerInterface
     */
    private $storeManager;
    
    /**
     * @var LoggerInterface
     */
    private $logger;
    
    public function __construct(
        ResourceConnection $resourceConnection,
        State $state,
        StoreManagerInterface $storeManager,
        LoggerInterface $logger,
        string $name = null
    ) {
        parent::__construct($name);
        $this->resourceConnection = $resourceConnection;
        $this->state = $state;
        $this->storeManager = $storeManager;
        $this->logger = $logger;
    }
    
    protected function configure()
    {
        $this->setName('catalog:product:check')
            ->setDescription('Check product details in database')
            ->addOption(
                self::SKU,
                's',
                InputOption::VALUE_OPTIONAL,
                'Product SKU to check'
            )
            ->addOption(
                self::ID,
                'i',
                InputOption::VALUE_OPTIONAL,
                'Product ID to check'
            );
            
        parent::configure();
    }
    
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        try {
            $this->state->setAreaCode(\Magento\Framework\App\Area::AREA_ADMINHTML);
            
            $sku = $input->getOption(self::SKU);
            $id = $input->getOption(self::ID);
            
            if (!$sku && !$id) {
                $output->writeln('<error>Please provide either a SKU or an ID</error>');
                return Command::FAILURE;
            }
            
            $connection = $this->resourceConnection->getConnection();
            
            // Get base product data
            $productTable = $this->resourceConnection->getTableName('catalog_product_entity');
            if ($sku) {
                $productQuery = $connection->select()
                    ->from($productTable)
                    ->where('sku = ?', $sku);
                $output->writeln("<info>Checking product with SKU: {$sku}</info>");
            } else {
                $productQuery = $connection->select()
                    ->from($productTable)
                    ->where('entity_id = ?', $id);
                $output->writeln("<info>Checking product with ID: {$id}</info>");
            }
            
            $productData = $connection->fetchRow($productQuery);
            
            if (!$productData) {
                $output->writeln('<error>Product not found in catalog_product_entity</error>');
                return Command::FAILURE;
            }
            
            $output->writeln('');
            $output->writeln('<comment>Product Basic Information:</comment>');
            $output->writeln("Entity ID: {$productData['entity_id']}");
            $output->writeln("SKU: {$productData['sku']}");
            $output->writeln("Type: {$productData['type_id']}");
            $output->writeln("Created At: {$productData['created_at']}");
            $output->writeln("Updated At: {$productData['updated_at']}");
            
            $entityId = $productData['entity_id'];
            $rowId = isset($productData['row_id']) ? $productData['row_id'] : $entityId;
            
            // Get store-website information
            $output->writeln('');
            $output->writeln('<comment>Store/Website Assignment:</comment>');
            $websiteTable = $this->resourceConnection->getTableName('catalog_product_website');
            $websiteQuery = $connection->select()
                ->from($websiteTable, ['website_id'])
                ->where('product_id = ?', $entityId);
                
            $websiteIds = $connection->fetchCol($websiteQuery);
            
            if (empty($websiteIds)) {
                $output->writeln('<error>Product is not assigned to any website</error>');
            } else {
                $websiteNames = [];
                foreach ($websiteIds as $websiteId) {
                    try {
                        $website = $this->storeManager->getWebsite($websiteId);
                        $websiteNames[] = "{$website->getId()}: {$website->getName()}";
                    } catch (\Exception $e) {
                        $websiteNames[] = "{$websiteId}: Unknown";
                    }
                }
                $output->writeln("Website IDs: " . implode(", ", $websiteIds));
                $output->writeln("Website Names: " . implode(", ", $websiteNames));
            }
            
            // Get status and visibility attributes
            $output->writeln('');
            $output->writeln('<comment>Key Attributes:</comment>');
            $attributeIds = $this->getAttributeIds($connection, ['status', 'visibility']);
            
            $intTable = $this->resourceConnection->getTableName('catalog_product_entity_int');
            foreach ($attributeIds as $code => $attrId) {
                $attrQuery = $connection->select()
                    ->from($intTable, ['value', 'store_id'])
                    ->where('attribute_id = ?', $attrId)
                    ->where('entity_id = ?', $entityId);
                    
                $attrValues = $connection->fetchAll($attrQuery);
                
                if (empty($attrValues)) {
                    $output->writeln("<error>{$code} attribute not set</error>");
                } else {
                    foreach ($attrValues as $attrValue) {
                        $storeId = $attrValue['store_id'];
                        $value = $attrValue['value'];
                        
                        $valueLabel = $this->getAttributeValueLabel($code, $value);
                        $storeName = $storeId == 0 ? 'All Stores' : $this->getStoreName($storeId);
                        
                        $output->writeln("{$code}: {$value} ({$valueLabel}) [Store: {$storeName}]");
                    }
                }
            }
            
            // Check stock status
            $output->writeln('');
            $output->writeln('<comment>Stock Information:</comment>');
            $stockTable = $this->resourceConnection->getTableName('cataloginventory_stock_item');
            $stockQuery = $connection->select()
                ->from($stockTable)
                ->where('product_id = ?', $entityId);
                
            $stockData = $connection->fetchRow($stockQuery);
            
            if (!$stockData) {
                $output->writeln('<error>No stock information found</error>');
            } else {
                $output->writeln("Is In Stock: {$stockData['is_in_stock']}");
                $output->writeln("Quantity: {$stockData['qty']}");
                $output->writeln("Stock Status: {$stockData['stock_status']}");
                $output->writeln("Use Config Manage Stock: {$stockData['use_config_manage_stock']}");
                $output->writeln("Manage Stock: {$stockData['manage_stock']}");
            }
            
            // Check if product exists in GraphQL queries
            $output->writeln('');
            $output->writeln('<comment>GraphQL Related Information:</comment>');
            
            // Additional GraphQL debug info
            try {
                $productCollection = $this->getProductCollection($entityId);
                $output->writeln("Product Collection SQL: " . $productCollection->getSelectSql(true));
                $count = $productCollection->count();
                $output->writeln("Product Count in Collection: {$count}");
            } catch (\Exception $e) {
                $output->writeln("<error>Error checking collection: {$e->getMessage()}</error>");
            }
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $output->writeln("<error>{$e->getMessage()}</error>");
            $this->logger->error($e->getMessage(), ['exception' => $e]);
            return Command::FAILURE;
        }
    }
    
    /**
     * Get attribute IDs for given attribute codes
     *
     * @param \Magento\Framework\DB\Adapter\AdapterInterface $connection
     * @param array $attributeCodes
     * @return array
     */
    private function getAttributeIds($connection, $attributeCodes)
    {
        $eavAttributeTable = $this->resourceConnection->getTableName('eav_attribute');
        $eavEntityTable = $this->resourceConnection->getTableName('eav_entity_type');
        
        $query = $connection->select()
            ->from(['ea' => $eavAttributeTable], ['attribute_code', 'attribute_id'])
            ->join(
                ['eet' => $eavEntityTable],
                'ea.entity_type_id = eet.entity_type_id',
                []
            )
            ->where('eet.entity_type_code = ?', 'catalog_product')
            ->where('ea.attribute_code IN (?)', $attributeCodes);
            
        $result = $connection->fetchPairs($query);
        return $result;
    }
    
    /**
     * Get human-readable label for attribute value
     *
     * @param string $code
     * @param int $value
     * @return string
     */
    private function getAttributeValueLabel($code, $value)
    {
        if ($code === 'status') {
            return $value == 1 ? 'Enabled' : 'Disabled';
        } elseif ($code === 'visibility') {
            $labels = [
                1 => 'Not Visible Individually',
                2 => 'Catalog',
                3 => 'Search',
                4 => 'Catalog, Search'
            ];
            return $labels[$value] ?? 'Unknown';
        }
        return 'Unknown';
    }
    
    /**
     * Get store name by ID
     *
     * @param int $storeId
     * @return string
     */
    private function getStoreName($storeId)
    {
        try {
            $store = $this->storeManager->getStore($storeId);
            return $storeId . ': ' . $store->getName();
        } catch (\Exception $e) {
            return $storeId . ': Unknown';
        }
    }
    
    /**
     * Get test product collection
     *
     * @param int $entityId
     * @return \Magento\Catalog\Model\ResourceModel\Product\Collection
     */
    private function getProductCollection($entityId)
    {
        $objectManager = \Magento\Framework\App\ObjectManager::getInstance();
        $collectionFactory = $objectManager->create(\Magento\Catalog\Model\ResourceModel\Product\CollectionFactory::class);
        $collection = $collectionFactory->create();
        $collection->addFieldToFilter('entity_id', $entityId);
        return $collection;
    }
}
